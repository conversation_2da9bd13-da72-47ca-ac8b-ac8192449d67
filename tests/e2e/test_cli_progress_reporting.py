from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from llama_tune.cli import app
from unittest.mock import patch, MagicMock
from llama_tune.core.data_models import OptimalConfiguration, BenchmarkResult, SystemProfile, ModelProfile

runner = CliRunner()

@patch('llama_tune.cli.BenchmarkingEngine') # Patch the class directly
@patch('llama_tune.cli.analyzer_run_feasibility_check') # Patch the imported function in CLI
@patch('llama_tune.cli.analyzer_get_model_profile') # Patch the imported function in CLI
@patch('llama_tune.cli.analyzer_get_system_profile') # Patch the imported function in CLI
@patch('rich.progress.Progress') # Patch Progress directly
@patch('typer.echo') # Patch typer.echo
@patch('sys.exit') # Patch sys.exit
@patch('os.path.exists') # Patch os.path.isfile') # Patch os.path.exists
@patch('os.path.isfile') # Patch os.path.isfile
@patch('llama_tune.analyzer.analyzer.subprocess.run') # Patch subprocess.run in analyzer
def test_benchmark_progress_reporting(
    mock_subprocess_run,
    mock_is_file,
    mock_exists,
    mock_sys_exit,
    mock_typer_echo,
    mock_progress_class,
    mock_get_system_profile,
    mock_get_model_profile,
    mock_run_feasibility_check,
    mock_benchmarking_engine_class
):
    """
    Tests that the benchmark command displays progress reporting elements.

    This test verifies that:
    1. The benchmark command executes successfully with minimal parameters
    2. Progress callbacks are called with expected phase descriptions (Phase 1/2, Phase 2/2)
    3. Run counter information is included (Run X/Y format)
    4. Speed information is provided in progress updates
    5. The completion message is displayed

    Addresses AC 2, 3, and 4 from Story 3.8.
    """
    # Prevent sys.exit from terminating the test prematurely
    mock_sys_exit.side_effect = lambda *args: None

    # Mock os.path.exists and os.path.isfile to simulate dummy model file
    mock_exists.return_value = True
    mock_is_file.return_value = True

    # Mock subprocess.run for llama-gguf calls
    mock_subprocess_run.return_value = MagicMock(
        stdout="""
model: arch: llama
model: n_layer: 32
model: ftype: Q4_0
""",
        stderr="",
        returncode=0
    )

    # Mock system and model profiles for BenchmarkingEngine's internal calls
    mock_get_system_profile.return_value = SystemProfile(total_ram_gb=16, cpu_cores=8, gpus=[], numa_detected=False, blas_backend="BLIS")
    mock_get_model_profile.return_value = ModelProfile(file_path="dummy/model.gguf", quantization_type="Q4_0", architecture="llama", layer_count=32)

    # Track progress callback calls for verification
    progress_calls = []

    # Configure the return value of run_benchmark directly on the mocked method
    def mock_run_benchmark_side_effect(*args, **kwargs):
        # The update_progress_callback is the 5th positional argument (index 4)
        update_callback = args[4] if len(args) > 4 else kwargs.get('update_progress_callback')
        if update_callback:
            # Simulate progress updates and track them
            calls = [
                ("Phase 1/2", "Initializing", 0, 100, None, 1, 1),
                ("Phase 1/2", "Running benchmark", 50, 100, 15.5, 1, 1),
                ("Phase 2/2", "Finalizing", 100, 100, None, 1, 1)
            ]
            for call_args in calls:
                progress_calls.append(call_args)
                update_callback(*call_args)

        return (
            OptimalConfiguration(
                system_profile=mock_get_system_profile.return_value,
                model_profile=mock_get_model_profile.return_value,
                best_benchmark_result=BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=10.0, generation_speed_tps=20.0, batch_size=1, parallel_level=1),
                generated_command="./llama.cpp/main -m dummy/model.gguf -p 'test'",
                notes=[],
                ctx_size=512,
                sampling_parameters={}
            ),
            [] # all_benchmark_results (empty list for this test)
        )

    # Create a MagicMock instance for BenchmarkingEngine and set its run_benchmark side effect
    mock_benchmarking_engine_instance = MagicMock()
    mock_benchmarking_engine_instance.run_benchmark.side_effect = mock_run_benchmark_side_effect
    mock_benchmarking_engine_class.return_value = mock_benchmarking_engine_instance

    # Mock the Progress class to avoid Rich console output during testing
    mock_progress_instance = MagicMock()
    mock_progress_instance.__enter__.return_value = mock_progress_instance
    mock_progress_instance.__exit__.return_value = None
    mock_progress_instance.add_task.return_value = "mock_task_id"
    mock_progress_class.return_value = mock_progress_instance

    result = runner.invoke(app, [
        "--benchmark",
        "--model-path", "dummy/model.gguf",
        "--ctx-size", "512",
        "--num-runs", "1"
    ])

    assert result.exit_code == 0

    # Verify that progress callbacks were called with expected patterns
    assert len(progress_calls) == 3, f"Expected 3 progress calls, got {len(progress_calls)}"

    # Check that phase descriptions are present
    phase_descriptions = [call[0] for call in progress_calls]
    assert "Phase 1/2" in phase_descriptions, f"Expected 'Phase 1/2' in {phase_descriptions}"
    assert "Phase 2/2" in phase_descriptions, f"Expected 'Phase 2/2' in {phase_descriptions}"

    # Check that run counters are present (run_idx and num_runs)
    run_info = [(call[5], call[6]) for call in progress_calls if call[5] is not None and call[6] is not None]
    assert len(run_info) > 0, "Expected run counter information (Run X/Y)"
    assert (1, 1) in run_info, f"Expected (1, 1) run info, got {run_info}"

    # Check that speed information is included in at least one call
    speeds = [call[4] for call in progress_calls if call[4] is not None]
    assert len(speeds) > 0, "Expected at least one progress call with speed information"

    # Verify that typer.echo was called with the completion message
    echo_calls = [call.args[0] for call in mock_typer_echo.call_args_list]
    assert any("Benchmarking complete!" in str(call) for call in echo_calls), f"Expected 'Benchmarking complete!' in echo calls: {echo_calls}"