
import pytest
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine
from llama_tune.core.data_models import BenchmarkResult, ModelProfile
import numpy as np

@pytest.fixture
def engine():
    return BenchmarkingEngine()

def test_calculate_performance_score(engine: BenchmarkingEngine):
    # Test case 1: Balanced speeds
    score1 = engine._calculate_performance_score(prompt_speed_tps=100, generation_speed_tps=50)
    assert score1 == (50 * 0.7) + (100 * 0.3)

    # Test case 2: High generation speed
    score2 = engine._calculate_performance_score(prompt_speed_tps=50, generation_speed_tps=200)
    assert score2 == (200 * 0.7) + (50 * 0.3)

    # Test case 3: Zero speeds
    score3 = engine._calculate_performance_score(prompt_speed_tps=0, generation_speed_tps=0)
    assert score3 == 0.0

def test_analyze_throughput_results_statistical_significance(engine: BenchmarkingEngine):
    # Mocking results with different means and standard deviations
    results = [
        BenchmarkResult(n_gpu_layers=10, prompt_speed_tps=100, generation_speed_tps=50, batch_size=8, parallel_level=4,
                        generation_speed_tps_mean=50, generation_speed_tps_std=5),
        BenchmarkResult(n_gpu_layers=10, prompt_speed_tps=110, generation_speed_tps=60, batch_size=16, parallel_level=4,
                        generation_speed_tps_mean=60, generation_speed_tps_std=2), # Higher mean, lower stddev -> better
        BenchmarkResult(n_gpu_layers=10, prompt_speed_tps=105, generation_speed_tps=60, batch_size=32, parallel_level=4,
                        generation_speed_tps_mean=60, generation_speed_tps_std=8), # Same mean, but higher stddev
    ]
    
    best_result = engine._analyze_throughput_results(results, n_gpu_layers=10)
    
    # The second result should be chosen as it has a higher composite score due to higher prompt speed
    # and the same generation speed but with a lower standard deviation, implying more stable performance.
    assert best_result.batch_size == 16
    assert best_result.generation_speed_tps_mean == 60
    assert best_result.generation_speed_tps_std == 2

def test_gpu_offload_benchmark_statistical_calculation(mocker, engine: BenchmarkingEngine):
    # Mock the helper that calls the external process
    mock_check_layers = mocker.patch.object(engine, '_check_n_gpu_layers')

    # Simulate successful runs with some variance
    mock_check_layers.side_effect = [
        # n_gpu_layers = 25 (mid of 0-50)
        BenchmarkResult(n_gpu_layers=25, prompt_speed_tps=100, generation_speed_tps=50, batch_size=None, parallel_level=None),
        BenchmarkResult(n_gpu_layers=25, prompt_speed_tps=102, generation_speed_tps=51, batch_size=None, parallel_level=None),
        BenchmarkResult(n_gpu_layers=25, prompt_speed_tps=98, generation_speed_tps=49, batch_size=None, parallel_level=None),
        # n_gpu_layers = 38 (mid of 26-50)
        BenchmarkResult(n_gpu_layers=38, prompt_speed_tps=120, generation_speed_tps=60, batch_size=None, parallel_level=None),
        BenchmarkResult(n_gpu_layers=38, prompt_speed_tps=122, generation_speed_tps=61, batch_size=None, parallel_level=None),
        BenchmarkResult(n_gpu_layers=38, prompt_speed_tps=118, generation_speed_tps=59, batch_size=None, parallel_level=None),
        # n_gpu_layers = 44 (mid of 39-50) -> This one will "fail"
        None, None, None
    ]

    mock_model_profile = ModelProfile(file_path="dummy.gguf", architecture="test", layer_count=50, quantization_type="Q4_K_M")
    
    best_result, all_successful_results = engine._run_gpu_offload_benchmark(
        model_profile=mock_model_profile,
        ctx_size=2048,
        num_runs=3,
        progress_callback=None
    )

    # The best result should be from n_gpu_layers=38
    assert best_result.n_gpu_layers == 38
    
    # Check if mean is calculated correctly
    expected_gen_mean = np.mean([60, 61, 59])
    assert best_result.generation_speed_tps_mean == pytest.approx(expected_gen_mean)
    
    # Check if stddev is calculated correctly (using numpy for reference)
    expected_gen_std = np.std([60, 61, 59], ddof=1) # ddof=1 for sample standard deviation
    assert best_result.generation_speed_tps_std == pytest.approx(expected_gen_std)
    
    # Check individual results are stored
    assert len(best_result.individual_results) == 3
