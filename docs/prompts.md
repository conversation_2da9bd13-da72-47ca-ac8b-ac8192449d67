# Prompts

## Full Stack Developer

### Implement Story
Pre-requisite:
In the IDE, insert specific story file path to the prompt below such as @/docs/stories/story-file.md

Prompt:
```
Please implement the story @docs/stories/3.9-implement-statistical-stability-check.md. Create or modify the necessary files in my workspace and write the required tests as specified. Ensure the code meets all acceptance criteria defined in the user story and aligns with the architecture document @docs/architecture.md. Execute tests with command `poetry run pytest -v --ignore=vendor` and fix any errors.
```

### Fix deviation and misalignment
Prompt:
```
Fix the following deviations or misalignments identified according to recommendations:
[Paste the list of deviations or misalignments here]

Create or modify the necessary files in my workspace and write the required tests as specified. Ensure the code meets all acceptance criteria defined in the user story document and aligns with the architecture document @docs/architecture.md. Execute tests with command `poetry run pytest -v --ignore=vendor` and fix any errors.
```

## Product Owner

### Review Code of Epic
Pre-requisite: 
- In the Gemini Gems's Fullstack team, switch to Product Owner agent (<PERSON>) with `*agent po`.
- Import code from GitHub repository such as https://github.com/limcheekin/llama-tune/tree/bmad

Prompt:
```
Hi <PERSON>, please carefully review the imported GitHub repository in detail, verify code implemented by the developer agent strictly adhere to the architecture document (docs/architecture.md) and detail user story documents (docs/stories/*.md) of the Epic X we defined, identify any deviation and misalignment.
```
**Note:** Need to change `X` to specific epic.

## QA/Code Reviewer

### Review Code of Story
Prompt:
```
Please carefully review the code and test cases implemented in detail, ensure it strictly adhere to the detail user story document (@docs/stories/3.9-implement-statistical-stability-check.md) and the architecture document (@docs/architecture.md). If you make any changes to the codebase, execute tests with command `poetry run pytest -v --ignore=vendor` and fix any errors.
```